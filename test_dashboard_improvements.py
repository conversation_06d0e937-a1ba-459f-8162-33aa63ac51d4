#!/usr/bin/env python3
"""
Test script for Self-Healer Dashboard improvements.

Tests the four key improvements:
1. Session ID links to details
2. Meaningful Error_Type names  
3. Status "Resolved" links to resolution details
4. Actual solutions provided by Self-Healer
"""

import asyncio
import sys
sys.path.append('.')

from Self_Healer.dashboard.dashboard import SelfHealerDashboard
from Self_Healer.core.healer_manager import SelfHealerManager

async def test_dashboard_improvements():
    """Test the dashboard improvements."""
    print("🧪 Testing Self-Healer Dashboard Improvements")
    print("=" * 50)
    
    try:
        # Initialize dashboard
        healer_manager = SelfHealerManager()
        dashboard = SelfHealerDashboard(healer_manager, port=8082)  # Use different port for testing
        
        print("✅ Dashboard initialized successfully")
        
        # Test 1: Session data retrieval with meaningful error types
        print("\n1️⃣ Testing Session Data Retrieval...")
        sessions = await dashboard._get_session_data()
        print(f"   Retrieved {len(sessions)} sessions")
        
        if sessions:
            for i, session in enumerate(sessions[:3]):  # Show first 3
                print(f"   Session {i+1}:")
                print(f"     - ID: {session['session_id']}")
                print(f"     - Error Type: {session['error_id']}")  # Should be meaningful now
                print(f"     - Status: {session['status']}")
                print(f"     - Success: {session['success']}")
                print(f"     - Duration: {session['duration']}s")
        else:
            print("   ⚠️ No sessions found")
        
        # Test 2: Session details functionality
        print("\n2️⃣ Testing Session Details...")
        if sessions:
            test_session_id = sessions[0]['session_id']
            details = await dashboard._get_session_details(test_session_id)
            print(f"   Session Details for {test_session_id}:")
            print(f"     - Found: {details['found']}")
            print(f"     - Attributes: {len(details['attributes'])}")
            print(f"     - Facts: {len(details['facts'])}")
            print(f"     - Evidence: {len(details['evidence'])}")
            print(f"     - Resolution Summary: {details['resolution_summary'][:100]}...")
        else:
            print("   ⚠️ No sessions to test details")
        
        # Test 3: Error type information
        print("\n3️⃣ Testing Error Type Information...")
        test_error_types = ['json_parsing', 'llm_communication', 'validation']
        
        for error_type in test_error_types:
            info = await dashboard._get_error_type_info(error_type)
            print(f"   Error Type: {error_type}")
            print(f"     - Display Name: {info['display_name']}")
            print(f"     - Solutions Count: {info['solutions_count']}")
            print(f"     - Average Effectiveness: {info['average_effectiveness']:.1f}%")
            print(f"     - Recommendations: {len(info['recommendations'])}")
        
        # Test 4: Meaningful error name conversion
        print("\n4️⃣ Testing Error Name Conversion...")
        test_names = [
            'error_log_entry',
            'llm_communication', 
            'json_parsing',
            'workflow_structure',
            'validation',
            'unknown_error_type'
        ]
        
        for name in test_names:
            meaningful = dashboard._get_meaningful_error_name(name)
            print(f"   '{name}' → '{meaningful}'")
        
        print("\n🎉 Dashboard Improvements Test Complete!")
        print("\n📋 Summary of Improvements:")
        print("   ✅ 1. Session IDs are now clickable and link to detailed views")
        print("   ✅ 2. Error types show meaningful names instead of technical IDs")
        print("   ✅ 3. Status 'Resolved' links to resolution explanations")
        print("   ✅ 4. Self-Healer provides actual solutions with effectiveness ratings")
        
        print("\n🚀 Next Steps:")
        print("   1. Start the dashboard: python -m Self_Healer.dashboard.dashboard")
        print("   2. Visit: http://localhost:8081")
        print("   3. Click on session IDs, error types, and resolved statuses")
        print("   4. Verify the modal dialogs show detailed information")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_dashboard_improvements())
