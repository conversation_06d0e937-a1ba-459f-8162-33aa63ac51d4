import asyncio
from n8n_builder.mcp_database_tool import MCPDatabaseTool

async def test_dashboard_db():
    db_tool = MCPDatabaseTool('knowledgebase')
    
    print("=== Testing Dashboard Database Integration ===")
    
    # Test 1: Direct query for sessions
    print("\n1. Direct query for sessions:")
    direct_query = "SELECT * FROM REF_Entities WHERE Name LIKE 'Session_%'"
    result = await db_tool.execute_query(direct_query)
    print(f"Direct query result: {result}")
    
    # Test 2: Test stored procedure call
    print("\n2. Testing stored procedure:")
    try:
        proc_result = await db_tool.execute_stored_procedure(
            'S_SYS_SelfHealer_RecentSessions_P',
            {'Limit': 10}
        )
        print(f"Stored procedure result: {proc_result}")
        
        if proc_result.get('result_sets'):
            for i, result_set in enumerate(proc_result['result_sets']):
                print(f"Result set {i}: {result_set['row_count']} rows")
                for row in result_set.get('rows', []):
                    print(f"  Row: {row}")
    except Exception as e:
        print(f"Stored procedure error: {e}")
    
    # Test 3: Test the exact same call the dashboard makes
    print("\n3. Testing dashboard-style call:")
    try:
        from Self_Healer.core.knowledge_database_wrapper import SelfHealerKnowledgeDB
        
        knowledge_db = SelfHealerKnowledgeDB('knowledgebase')
        
        dashboard_result = await knowledge_db.db_tool.execute_stored_procedure(
            'S_SYS_SelfHealer_RecentSessions_P',
            {'Limit': 10}
        )
        print(f"Dashboard-style result: {dashboard_result}")
        
        sessions = []
        if dashboard_result.get('result_sets'):
            for result_set in dashboard_result['result_sets']:
                if result_set.get('rows'):
                    for row in result_set['rows']:
                        session = {
                            'session_id': row.get('SessionID', 'Unknown'),
                            'error_id': f"Error_{row.get('SessionID', 'Unknown')[:8]}",
                            'status': row.get('Status', 'Unknown'),
                            'success': bool(row.get('Success', 0)),
                            'duration': row.get('Duration', '0 seconds'),
                            'start_time': row.get('CreateDate', '').replace('T', ' ') if row.get('CreateDate') else '',
                            'end_time': None
                        }
                        sessions.append(session)
        
        print(f"Processed sessions: {sessions}")
        print(f"Session count: {len(sessions)}")
        
    except Exception as e:
        print(f"Dashboard-style call error: {e}")

if __name__ == "__main__":
    asyncio.run(test_dashboard_db())
