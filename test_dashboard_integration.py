import asyncio
from n8n_builder.mcp_database_tool import MCPDatabaseTool

async def test_dashboard_integration():
    print("=== TESTING DASHBOARD KNOWLEDGEBASE INTEGRATION ===")
    
    # Test the exact same calls the dashboard makes
    try:
        from Self_Healer.core.knowledge_database_wrapper import SelfHealerKnowledgeDB
        
        knowledge_db = SelfHealerKnowledgeDB('knowledgebase')
        
        print("\n1. Testing session retrieval (dashboard _get_session_data):")
        session_result = await knowledge_db.db_tool.execute_stored_procedure(
            'S_SYS_SelfHealer_RecentSessions_P',
            {'Limit': 10}
        )
        
        sessions = []
        if session_result.get('result_sets'):
            for result_set in session_result['result_sets']:
                if result_set.get('rows'):
                    for row in result_set['rows']:
                        session = {
                            'session_id': row.get('SessionID', 'Unknown'),
                            'error_id': f"Error_{row.get('SessionID', 'Unknown')[:8]}",
                            'status': row.get('Status', 'Unknown'),
                            'success': bool(row.get('Success', 0)),
                            'duration': row.get('Duration', '0 seconds'),
                            'start_time': row.get('CreateDate', '').replace('T', ' ') if row.get('CreateDate') else '',
                            'end_time': None
                        }
                        sessions.append(session)
        
        print(f"✅ Sessions retrieved: {len(sessions)}")
        for session in sessions:
            print(f"  - {session['session_id']}: {session['status']} ({session['start_time']})")
        
        print("\n2. Testing analytics retrieval (dashboard _get_session_analytics):")
        analytics_result = await knowledge_db.db_tool.execute_stored_procedure(
            'S_SYS_SelfHealer_SessionAnalytics_P',
            {'DaysBack': 30}
        )
        
        analytics = {}
        if analytics_result.get('result_sets') and len(analytics_result['result_sets']) > 0:
            result_set = analytics_result['result_sets'][0]
            if result_set.get('rows') and len(result_set['rows']) > 0:
                row = result_set['rows'][0]
                analytics = {
                    'TotalSessions': row.get('TotalSessions', 0),
                    'SuccessfulSessions': row.get('SuccessfulSessions', 0),
                    'FailedSessions': row.get('FailedSessions', 0),
                    'SuccessRate': float(row.get('SuccessRate', 0.0)),
                    'AverageHealingTime': float(row.get('AverageHealingTime', 0.0)),
                    'SessionsLast24Hours': row.get('SessionsLast24Hours', 0),
                    'SessionsLastWeek': row.get('SessionsLastWeek', 0)
                }
        
        print(f"✅ Analytics retrieved: {analytics}")
        
        print("\n3. Summary:")
        print(f"  - Sessions found: {len(sessions)}")
        print(f"  - Total sessions in analytics: {analytics.get('TotalSessions', 0)}")
        print(f"  - Success rate: {analytics.get('SuccessRate', 0)}%")
        print(f"  - Sessions last 24h: {analytics.get('SessionsLast24Hours', 0)}")
        
        if len(sessions) > 0 and analytics.get('TotalSessions', 0) > 0:
            print("\n🎉 DASHBOARD KNOWLEDGEBASE INTEGRATION IS WORKING!")
            print("The dashboard should now show sessions from the KnowledgeBase.")
        else:
            print("\n❌ No sessions found - dashboard will still show empty.")
            
    except Exception as e:
        print(f"❌ Error testing dashboard integration: {e}")

if __name__ == "__main__":
    asyncio.run(test_dashboard_integration())
