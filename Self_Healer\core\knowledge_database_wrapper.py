"""
Knowledge Database Wrapper for Self-Healer
Provides high-level interface to KnowledgeBase using stored procedures
"""

import logging
from typing import Dict, List, Any, Optional
from n8n_builder.mcp_database_tool import MCPDatabaseTool

logger = logging.getLogger(__name__)


class SelfHealerKnowledgeDB:
    """
    High-level wrapper for Self-Healer KnowledgeBase operations using stored procedures.
    
    This class provides a clean interface to the KnowledgeBase using the new stored procedures
    following the established SQL naming conventions. It replaces direct SQL queries with
    optimized stored procedure calls for better performance and maintainability.
    """
    
    def __init__(self, connection_name: str = 'knowledgebase'):
        """Initialize the knowledge database wrapper."""
        self.db_tool = MCPDatabaseTool(connection_name)
        self.logger = logger
        self.logger.debug("Self-Healer Knowledge Database Wrapper initialized")
    
    async def get_session_attributes(self, entity_id: int) -> Dict[str, Any]:
        """
        Get session attributes for a specific entity ID using stored procedure.
        
        Args:
            entity_id: The entity ID to retrieve attributes for
            
        Returns:
            Dictionary with status and attribute data
        """
        try:
            result = await self.db_tool.execute_stored_procedure(
                'S_SYS_XRF_EntityAttributeValue_P_EntityID',
                {'EntityID': entity_id}
            )
            
            if result['status'] == 'success' and result['result_sets']:
                return {
                    'status': 'success',
                    'entity_id': entity_id,
                    'attributes': result['result_sets'][0]['rows'],
                    'attribute_count': result['result_sets'][0]['row_count']
                }
            else:
                return {
                    'status': 'error',
                    'error': 'No attributes found or procedure failed',
                    'entity_id': entity_id
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get session attributes for entity {entity_id}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'entity_id': entity_id
            }
    
    async def get_facts_by_error_type(self, error_type: str) -> Dict[str, Any]:
        """
        Get facts related to a specific error type using stored procedure.
        
        Args:
            error_type: The error type to search for (supports wildcards)
            
        Returns:
            Dictionary with status and facts data
        """
        try:
            result = await self.db_tool.execute_stored_procedure(
                'S_SYS_REF_Fact_P_ErrorType',
                {'ErrorType': error_type}
            )
            
            if result['status'] == 'success' and result['result_sets']:
                facts = result['result_sets'][0]['rows']
                return {
                    'status': 'success',
                    'error_type': error_type,
                    'facts': facts,
                    'fact_count': len(facts),
                    'average_validity': sum(float(f['ValidityRating']) for f in facts) / len(facts) if facts else 0
                }
            else:
                return {
                    'status': 'error',
                    'error': 'No facts found or procedure failed',
                    'error_type': error_type
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get facts for error type {error_type}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': error_type
            }
    
    async def get_opinions_by_error_type(self, error_type: str) -> Dict[str, Any]:
        """
        Get opinions related to a specific error type using stored procedure.
        
        Args:
            error_type: The error type to search for (supports wildcards)
            
        Returns:
            Dictionary with status and opinions data
        """
        try:
            result = await self.db_tool.execute_stored_procedure(
                'S_SYS_REF_Opinion_P_ErrorType',
                {'ErrorType': error_type}
            )
            
            if result['status'] == 'success' and result['result_sets']:
                opinions = result['result_sets'][0]['rows']
                return {
                    'status': 'success',
                    'error_type': error_type,
                    'opinions': opinions,
                    'opinion_count': len(opinions),
                    'average_validity': sum(float(o['ValidityRating']) for o in opinions) / len(opinions) if opinions else 0
                }
            else:
                return {
                    'status': 'error',
                    'error': 'No opinions found or procedure failed',
                    'error_type': error_type
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get opinions for error type {error_type}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': error_type
            }
    
    async def get_solution_analytics(self, category: Optional[str] = None, 
                                   min_validity: float = 0, limit: int = 50) -> Dict[str, Any]:
        """
        Get solution effectiveness analytics using stored procedure.
        
        Args:
            category: Optional category filter
            min_validity: Minimum validity rating filter
            limit: Maximum number of results
            
        Returns:
            Dictionary with analytics data
        """
        try:
            result = await self.db_tool.execute_stored_procedure(
                'S_SYS_REF_Fact_SelfHealer_Analytics_Prms',
                {
                    'Category': category,
                    'MinValidity': min_validity,
                    'Limit': limit
                }
            )
            
            if result['status'] == 'success' and result['result_sets']:
                solutions = result['result_sets'][0]['rows']
                
                # Calculate additional metrics
                total_solutions = len(solutions)
                trending_solutions = [s for s in solutions if s.get('IsTrending', 0) == 1]
                high_validity_solutions = [s for s in solutions if float(s['ValidityRating']) >= 80.0]
                
                return {
                    'status': 'success',
                    'category': category,
                    'min_validity': min_validity,
                    'solutions': solutions,
                    'metrics': {
                        'total_solutions': total_solutions,
                        'trending_count': len(trending_solutions),
                        'high_validity_count': len(high_validity_solutions),
                        'average_effectiveness': solutions[0].get('OverallAverage', 0) if solutions else 0
                    },
                    'top_solutions': solutions[:5],
                    'trending_solutions': trending_solutions[:5]
                }
            else:
                return {
                    'status': 'error',
                    'error': 'No analytics data found or procedure failed',
                    'category': category
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get solution analytics: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'category': category
            }
    
    async def search_knowledge(self, search_query: str, knowledge_type: Optional[str] = None,
                             min_validity: float = 0, limit: int = 10) -> Dict[str, Any]:
        """
        Comprehensive knowledge search using stored procedure.
        
        Args:
            search_query: Search term to find across all knowledge
            knowledge_type: Type filter: 'fact', 'opinion', 'evidence', or None for all
            min_validity: Minimum validity rating filter
            limit: Maximum results per knowledge type
            
        Returns:
            Dictionary with search results
        """
        try:
            result = await self.db_tool.execute_stored_procedure(
                'S_SYS_SelfHealer_KnowledgeSearch_Prms',
                {
                    'SearchQuery': search_query,
                    'KnowledgeType': knowledge_type,
                    'MinValidity': min_validity,
                    'Limit': limit
                }
            )
            
            if result['status'] == 'success' and result['result_sets']:
                # Organize results by knowledge type
                search_results = {
                    'facts': [],
                    'opinions': [],
                    'evidence': []
                }
                
                # Process each result set
                for result_set in result['result_sets']:
                    rows = result_set['rows']
                    if rows:
                        # Determine type from first row
                        first_row_type = rows[0].get('KnowledgeType', 'unknown')
                        if first_row_type in search_results:
                            search_results[first_row_type] = rows
                
                total_results = sum(len(results) for results in search_results.values())
                
                return {
                    'status': 'success',
                    'search_query': search_query,
                    'knowledge_type': knowledge_type,
                    'min_validity': min_validity,
                    'results': search_results,
                    'total_results': total_results,
                    'result_counts': {
                        'facts': len(search_results['facts']),
                        'opinions': len(search_results['opinions']),
                        'evidence': len(search_results['evidence'])
                    }
                }
            else:
                return {
                    'status': 'error',
                    'error': 'No search results found or procedure failed',
                    'search_query': search_query
                }
                
        except Exception as e:
            self.logger.error(f"Failed to search knowledge for '{search_query}': {e}")
            return {
                'status': 'error',
                'error': str(e),
                'search_query': search_query
            }


# Global instance for easy access
knowledge_db = None

def get_selfhealer_knowledge_db(connection_name: str = 'knowledgebase') -> SelfHealerKnowledgeDB:
    """Get or create Self-Healer Knowledge Database wrapper instance."""
    global knowledge_db
    if knowledge_db is None:
        knowledge_db = SelfHealerKnowledgeDB(connection_name)
    return knowledge_db
