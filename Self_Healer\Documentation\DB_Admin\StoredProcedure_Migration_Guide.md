# 🔄 Stored Procedure Migration Guide

**Created**: 2025-06-28  
**Purpose**: Guide for migrating Self-Healer from direct SQL queries to stored procedures  
**Scope**: Database architecture enhancement for better performance and maintainability  

## 📋 Overview

This guide documents the migration from direct SQL queries to stored procedures in the Self-Healer KnowledgeBase integration. The migration follows established SQL naming conventions and provides better performance, security, and maintainability.

## 🎯 Migration Benefits

### **Performance Improvements**
- **Query Plan Caching**: Stored procedures are pre-compiled and cached
- **Reduced Network Traffic**: Only procedure name and parameters sent
- **Optimized Execution**: SQL Server can optimize procedure execution paths

### **Security Enhancements**
- **SQL Injection Prevention**: Parameterized procedures eliminate injection risks
- **Access Control**: Granular permissions on procedure level
- **Audit Trail**: Better tracking of database operations

### **Maintainability Benefits**
- **Centralized Logic**: Business logic contained in database layer
- **Version Control**: Procedures can be versioned and deployed consistently
- **Easier Testing**: Individual procedures can be tested independently

## 📊 Migration Mapping

### **Before: Direct SQL Queries**

```python
# Old approach in knowledge_integration.py
attributes_query = """
SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
FROM XRF_EntityAttributeValue eav
JOIN REF_Attributes a ON eav.AttributeID = a.ID
JOIN REF_EntityValues ev ON eav.EntityValueID = ev.ID
WHERE eav.EntityID = ?
"""
attributes_result = await self.db_tool.execute_query(attributes_query, [entity_id])
```

### **After: Stored Procedure Calls**

```python
# New approach using stored procedures
result = await self.db_tool.execute_stored_procedure(
    'S_SYS_XRF_EntityAttributeValue_P_EntityID',
    {'EntityID': entity_id}
)
```

## 🔧 Created Stored Procedures

### **1. Session Attributes Retrieval**
- **Procedure**: `S_SYS_XRF_EntityAttributeValue_P_EntityID`
- **Purpose**: Get session attributes for specific entity
- **Parameters**: `@EntityID INT`
- **Replaces**: Direct query in `get_session_knowledge()`

### **2. Facts by Error Type**
- **Procedure**: `S_SYS_REF_Fact_P_ErrorType`
- **Purpose**: Get facts related to specific error types
- **Parameters**: `@ErrorType NVARCHAR(255)`
- **Replaces**: Direct query in `get_error_type_knowledge()`

### **3. Opinions by Error Type**
- **Procedure**: `S_SYS_REF_Opinion_P_ErrorType`
- **Purpose**: Get opinions related to specific error types
- **Parameters**: `@ErrorType NVARCHAR(255)`
- **Replaces**: Direct query in `get_error_type_knowledge()`

### **4. Solution Analytics**
- **Procedure**: `S_SYS_REF_Fact_SelfHealer_Analytics_Prms`
- **Purpose**: Get solution effectiveness analytics
- **Parameters**: `@Category`, `@MinValidity`, `@Limit`
- **Replaces**: Complex analytics query in API endpoints

### **5. Knowledge Search**
- **Procedure**: `S_SYS_SelfHealer_KnowledgeSearch_Prms`
- **Purpose**: Comprehensive search across all knowledge types
- **Parameters**: `@SearchQuery`, `@KnowledgeType`, `@MinValidity`, `@Limit`
- **Replaces**: Multiple search queries in API endpoints

## 🛠️ Enhanced MCP Database Tool

### **New Features Added**

```python
async def execute_stored_procedure(self, procedure_name: str, 
                                 parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Execute a stored procedure with named parameters."""
```

### **Key Improvements**
- **Named Parameter Support**: Use dictionary for parameter passing
- **Multiple Result Sets**: Handle procedures returning multiple result sets
- **Better Error Handling**: Detailed error reporting for procedure failures
- **Type Conversion**: Automatic handling of datetime and decimal types

## 📦 Knowledge Database Wrapper

### **New High-Level Interface**

Created `SelfHealerKnowledgeDB` class in `knowledge_database_wrapper.py`:

```python
# High-level methods for common operations
await knowledge_db.get_session_attributes(entity_id)
await knowledge_db.get_facts_by_error_type(error_type)
await knowledge_db.get_opinions_by_error_type(error_type)
await knowledge_db.get_solution_analytics(category, min_validity, limit)
await knowledge_db.search_knowledge(query, knowledge_type, min_validity, limit)
```

### **Benefits of Wrapper**
- **Simplified Interface**: Clean, intuitive method names
- **Error Handling**: Consistent error handling across all operations
- **Result Processing**: Automatic calculation of metrics and summaries
- **Type Safety**: Proper typing for all parameters and return values

## 🔄 Migration Steps

### **Phase 1: Install Stored Procedures**
1. **Execute SQL Script**: Run `create_selfhealer_procedures.sql`
2. **Verify Installation**: Check procedures exist in database
3. **Test Procedures**: Run basic tests to ensure functionality

### **Phase 2: Update Code**
1. **Import New Wrapper**: Add `SelfHealerKnowledgeDB` import
2. **Replace Direct Queries**: Update methods to use wrapper
3. **Update Error Handling**: Adapt to new result format
4. **Test Integration**: Verify all functionality works

### **Phase 3: Optimize and Monitor**
1. **Performance Testing**: Compare before/after performance
2. **Monitor Usage**: Track procedure execution statistics
3. **Optimize Queries**: Fine-tune procedures based on usage patterns

## 📝 Code Migration Examples

### **Example 1: Session Attributes**

**Before:**
```python
attributes_query = """
SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
FROM XRF_EntityAttributeValue eav
JOIN REF_Attributes a ON eav.AttributeID = a.ID
JOIN REF_EntityValues ev ON eav.EntityValueID = ev.ID
WHERE eav.EntityID = ?
"""
attributes_result = await self.db_tool.execute_query(attributes_query, [entity_id])
```

**After:**
```python
from Self_Healer.core.knowledge_database_wrapper import get_selfhealer_knowledge_db

knowledge_db = get_selfhealer_knowledge_db()
attributes_result = await knowledge_db.get_session_attributes(entity_id)
```

### **Example 2: Error Type Facts**

**Before:**
```python
facts_query = """
SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
FROM REF_Fact f
WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
AND f.DataSource LIKE '%Self-Healer%'
ORDER BY f.ValidityRating DESC
"""
facts_result = await self.db_tool.execute_query(facts_query, [f"%{error_type}%", f"%{error_type}%"])
```

**After:**
```python
facts_result = await knowledge_db.get_facts_by_error_type(error_type)
```

## 🧪 Testing Strategy

### **Test Files Created**
- **`test_stored_procedures.py`**: Comprehensive test suite
- **Database Connection Tests**: Verify connectivity
- **Procedure Existence Tests**: Confirm all procedures installed
- **Functionality Tests**: Test each procedure with sample data

### **Running Tests**
```bash
# Run the test suite
python tests/test_stored_procedures.py

# Or use pytest
pytest tests/test_stored_procedures.py -v
```

## 📊 Performance Monitoring

### **Key Metrics to Track**
- **Execution Time**: Compare before/after query execution times
- **Resource Usage**: Monitor CPU and memory usage
- **Cache Hit Rates**: Track procedure plan cache effectiveness
- **Error Rates**: Monitor for any procedure execution failures

### **SQL Server Monitoring Queries**
```sql
-- Check procedure execution statistics
SELECT 
    p.name,
    s.execution_count,
    s.total_elapsed_time,
    s.avg_elapsed_time
FROM sys.procedures p
JOIN sys.dm_exec_procedure_stats s ON p.object_id = s.object_id
WHERE p.name LIKE '%SYS%SelfHealer%'
ORDER BY s.avg_elapsed_time DESC
```

## 🎯 Next Steps

### **Immediate Actions**
1. **Install Procedures**: Execute the SQL script
2. **Run Tests**: Verify everything works correctly
3. **Update Integration**: Migrate knowledge_integration.py
4. **Update API Endpoints**: Migrate API layer to use wrapper

### **Future Enhancements**
1. **Additional Procedures**: Create procedures for insert/update operations
2. **Performance Tuning**: Optimize based on usage patterns
3. **Monitoring Dashboard**: Create real-time performance monitoring
4. **Documentation Updates**: Update all related documentation

## 📞 Support and Troubleshooting

### **Common Issues**
- **Procedure Not Found**: Verify SQL script executed successfully
- **Parameter Errors**: Check parameter names match procedure definition
- **Permission Issues**: Ensure database user has EXECUTE permissions

### **Debugging Tips**
- **Check Procedure Exists**: `SELECT name FROM sys.objects WHERE type = 'P'`
- **Test Procedure Directly**: Execute procedures in SQL Server Management Studio
- **Review Error Logs**: Check both application and SQL Server logs

---

**Key Principle**: This migration enhances performance and maintainability while preserving all existing functionality and following established SQL naming conventions.
